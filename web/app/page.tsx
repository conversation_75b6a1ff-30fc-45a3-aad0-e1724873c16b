'use client';

import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0">
        {/* 几何图形装饰 */}
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-full opacity-60"></div>
        <div className="absolute top-40 left-10 w-32 h-32 bg-gradient-to-br from-purple-50 to-pink-100 rounded-full opacity-40"></div>
        <div className="absolute bottom-32 right-1/3 w-48 h-48 bg-gradient-to-br from-green-50 to-emerald-100 rounded-full opacity-50"></div>

        {/* 微妙的网格背景 */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.01)_1px,transparent_1px)] bg-[size:60px_60px]"></div>
      </div>

      {/* 导航栏 */}
      <nav className="relative z-20 bg-white/80 backdrop-blur-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-bold">X</span>
              </div>
              <span className="text-xl font-bold text-gray-900">XXX管理平台</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => {
                  const featuresSection = document.getElementById('features-section');
                  featuresSection?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
              >
                产品功能
              </button>
              <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors cursor-pointer">解决方案</a>
              <a href="#" className="text-gray-600 hover:text-gray-900 transition-colors cursor-pointer">关于我们</a>
              <button
                onClick={() => {
                  const featuresSection = document.getElementById('features-section');
                  featuresSection?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors cursor-pointer"
              >
                立即体验
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <div className="relative z-10">
        {/* Hero Section */}
        <div className="max-w-7xl mx-auto py-24 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 左侧内容 */}
            <div className="text-left">
              <div className="mb-6">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200">
                  🚀 智能体管理平台
                </span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                下一代
                <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  AI驱动
                </span>
                <br />
                管理平台
              </h1>

              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                集成智能问数、政策管理、内容创作、财务对账等核心功能，
                为企业提供全方位的AI解决方案，助力数字化转型升级。
              </p>

              {/* CTA按钮 */}
              <div className="flex flex-col sm:flex-row gap-4 mb-12">
                <button
                  onClick={() => {
                    const featuresSection = document.getElementById('features-section');
                    featuresSection?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="inline-flex items-center justify-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg cursor-pointer"
                >
                  立即体验
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </button>

                <button className="inline-flex items-center justify-center px-8 py-4 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  观看演示
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h10a2 2 0 012 2v8a2 2 0 01-2 2H7a2 2 0 01-2-2v-8a2 2 0 012-2z" />
                  </svg>
                </button>
              </div>

              {/* 特色标签 */}
              <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  免费试用
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  7x24技术支持
                </div>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  企业级安全
                </div>
              </div>
            </div>

            {/* 右侧图片/演示 */}
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8 shadow-xl">
                <div className="bg-white rounded-xl p-6 shadow-lg">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <span className="text-sm text-gray-500">XXX管理平台</span>
                  </div>

                  <div className="space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-blue-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                    <div className="grid grid-cols-2 gap-4 mt-6">
                      <div className="h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">🧠</span>
                      </div>
                      <div className="h-20 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">🤝</span>
                      </div>
                      <div className="h-20 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">✍️</span>
                      </div>
                      <div className="h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">📊</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 浮动元素 */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                AI
              </div>
            </div>
          </div>
        </div>
        {/* 产品功能展示 */}
        <div id="features-section" className="bg-gray-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">核心功能模块</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                选择您需要的功能模块，开始您的AI智能体验之旅
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

              {/* 智能问数 */}
              <Link
                href="/intelligent-qa"
                className="group bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 cursor-pointer"
              >
                <div className="w-14 h-14 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-200 transition-colors">
                  <span className="text-2xl">🧠</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  智能问数
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  基于AI的智能数据查询与分析系统，自然语言交互，快速获取业务洞察
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">开发中</span>
                  <div className="flex items-center text-blue-600 text-sm font-medium group-hover:translate-x-1 transition-transform cursor-pointer">
                    了解更多
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </Link>

              {/* 代理人政策问答 */}
              <Link
                href="/agent-policy"
                className="group bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 cursor-pointer"
              >
                <div className="w-14 h-14 bg-green-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-200 transition-colors">
                  <span className="text-2xl">🤝</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                  代理人政策问答
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  代理人政策管理与智能问答系统，支持多模态文件导入和数据分析
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已上线</span>
                  <div className="flex items-center text-green-600 text-sm font-medium group-hover:translate-x-1 transition-transform cursor-pointer">
                    立即使用
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </Link>

              {/* 多功能创作与新闻引擎 */}
              <Link
                href="/content-engine"
                className="group bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 cursor-pointer"
              >
                <div className="w-14 h-14 bg-purple-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-purple-200 transition-colors">
                  <span className="text-2xl">✍️</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                  多功能创作引擎
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  AI驱动的内容创作与新闻生成平台，提供智能写作和编辑助手
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-purple-600 bg-purple-50 px-2 py-1 rounded-full">开发中</span>
                  <div className="flex items-center text-purple-600 text-sm font-medium group-hover:translate-x-1 transition-transform cursor-pointer">
                    了解更多
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </Link>

              {/* 智能对账工具 */}
              <Link
                href="/reconciliation-tool"
                className="group bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 cursor-pointer"
              >
                <div className="w-14 h-14 bg-orange-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-orange-200 transition-colors">
                  <span className="text-2xl">📊</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors">
                  智能对账工具
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  自动化财务对账与数据核验系统，大幅提升财务处理效率
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full">开发中</span>
                  <div className="flex items-center text-orange-600 text-sm font-medium group-hover:translate-x-1 transition-transform cursor-pointer">
                    了解更多
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              </Link>

            </div>
          </div>
        </div>

        {/* 平台优势 */}
        <div className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">为什么选择我们</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                领先的技术优势，专业的服务团队，为您提供最优质的AI解决方案
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
              <div className="text-center group">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <span className="text-3xl">🚀</span>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">高效智能</h3>
                <p className="text-gray-600 leading-relaxed">
                  基于最新AI技术，自动化处理复杂任务，大幅提升工作效率，
                  让您的团队专注于更有价值的创新工作。
                </p>
                <div className="mt-6 flex justify-center space-x-4 text-sm text-gray-500">
                  <span>• 自动化处理</span>
                  <span>• 智能分析</span>
                  <span>• 效率提升</span>
                </div>
              </div>

              <div className="text-center group">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <span className="text-3xl">🔒</span>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">安全可靠</h3>
                <p className="text-gray-600 leading-relaxed">
                  企业级安全架构，多层数据加密，严格的权限控制，
                  确保您的数据安全和业务连续性。
                </p>
                <div className="mt-6 flex justify-center space-x-4 text-sm text-gray-500">
                  <span>• 数据加密</span>
                  <span>• 权限控制</span>
                  <span>• 安全审计</span>
                </div>
              </div>

              <div className="text-center group">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <span className="text-3xl">⚡</span>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">快速响应</h3>
                <p className="text-gray-600 leading-relaxed">
                  毫秒级响应速度，实时数据处理，7x24小时稳定运行，
                  随时随地为您提供可靠的服务支持。
                </p>
                <div className="mt-6 flex justify-center space-x-4 text-sm text-gray-500">
                  <span>• 实时处理</span>
                  <span>• 7x24支持</span>
                  <span>• 高可用性</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 数据统计 */}
        <div className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">平台数据</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                实时更新的平台使用统计，见证我们的成长与用户的信任
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center group hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">1,234+</div>
                <div className="text-lg font-medium text-gray-700 mb-1">活跃用户</div>
                <div className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded-full inline-block">
                  月增长 +15%
                </div>
              </div>

              <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center group hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">5,678+</div>
                <div className="text-lg font-medium text-gray-700 mb-1">处理任务</div>
                <div className="text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded-full inline-block">
                  日均处理
                </div>
              </div>

              <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center group hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">98.9%</div>
                <div className="text-lg font-medium text-gray-700 mb-1">系统可用性</div>
                <div className="text-sm text-purple-600 bg-purple-50 px-2 py-1 rounded-full inline-block">
                  SLA保障
                </div>
              </div>

              <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center group hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
                  </svg>
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">24/7</div>
                <div className="text-lg font-medium text-gray-700 mb-1">技术支持</div>
                <div className="text-sm text-orange-600 bg-orange-50 px-2 py-1 rounded-full inline-block">
                  全天候服务
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 系统状态 */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
              <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">系统状态监控</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-gray-700 font-medium">智能问数</span>
                  </div>
                  <span className="text-green-600 text-sm">正常</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-gray-700 font-medium">政策问答</span>
                  </div>
                  <span className="text-green-600 text-sm">正常</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                    <span className="text-gray-700 font-medium">创作引擎</span>
                  </div>
                  <span className="text-yellow-600 text-sm">开发中</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                    <span className="text-gray-700 font-medium">对账工具</span>
                  </div>
                  <span className="text-yellow-600 text-sm">开发中</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
              {/* 品牌信息 */}
              <div className="md:col-span-1">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm font-bold">X</span>
                  </div>
                  <span className="text-xl font-bold">XXX管理平台</span>
                </div>
                <p className="text-gray-400 mb-6">
                  下一代AI驱动的智能体管理平台，为企业提供全方位的数字化解决方案。
                </p>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                    </svg>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                    </svg>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </a>
                </div>
              </div>

              {/* 产品链接 */}
              <div>
                <h4 className="font-semibold mb-4">产品功能</h4>
                <div className="space-y-3 text-gray-400">
                  <a href="/intelligent-qa" className="block hover:text-white transition-colors cursor-pointer">智能问数</a>
                  <a href="/agent-policy" className="block hover:text-white transition-colors cursor-pointer">政策问答</a>
                  <a href="/content-engine" className="block hover:text-white transition-colors cursor-pointer">创作引擎</a>
                  <a href="/reconciliation-tool" className="block hover:text-white transition-colors cursor-pointer">对账工具</a>
                </div>
              </div>

              {/* 支持链接 */}
              <div>
                <h4 className="font-semibold mb-4">支持服务</h4>
                <div className="space-y-3 text-gray-400">
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">帮助中心</a>
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">API文档</a>
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">技术支持</a>
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">状态页面</a>
                </div>
              </div>

              {/* 公司信息 */}
              <div>
                <h4 className="font-semibold mb-4">公司信息</h4>
                <div className="space-y-3 text-gray-400">
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">关于我们</a>
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">联系我们</a>
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">隐私政策</a>
                  <a href="#" className="block hover:text-white transition-colors cursor-pointer">服务条款</a>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                © 2025 XXX管理平台. 保留所有权利.
              </p>
              <div className="flex space-x-6 mt-4 md:mt-0 text-sm text-gray-400">
                <a href="#" className="hover:text-white transition-colors cursor-pointer">隐私政策</a>
                <a href="#" className="hover:text-white transition-colors cursor-pointer">服务条款</a>
                <a href="#" className="hover:text-white transition-colors cursor-pointer">Cookie政策</a>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
