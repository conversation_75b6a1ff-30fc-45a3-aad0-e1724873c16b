'use client';

import Link from 'next/link';

export default function ContentEnginePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* 返回首页按钮 */}
          <div className="mb-8 text-left">
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
            >
              ← 返回首页
            </Link>
          </div>

          <div className="text-6xl mb-8">✍️</div>
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            多功能创作与新闻引擎
          </h1>
          <p className="text-xl text-gray-600 mb-16 max-w-3xl mx-auto">
            AI驱动的内容创作与新闻生成平台
          </p>
          
          {/* 功能介绍 */}
          <div className="bg-white/80 backdrop-blur-sm p-12 rounded-3xl shadow-lg max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              功能开发中
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              多功能创作与新闻引擎正在紧张开发中，敬请期待！
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-purple-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-purple-900 mb-2">智能写作</h3>
                <p className="text-purple-700">AI辅助文章创作，提供多种写作风格和模板</p>
              </div>
              <div className="bg-indigo-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-indigo-900 mb-2">新闻生成</h3>
                <p className="text-indigo-700">自动抓取热点，生成新闻摘要和深度报道</p>
              </div>
              <div className="bg-pink-50 p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-pink-900 mb-2">内容优化</h3>
                <p className="text-pink-700">SEO优化建议，提升内容传播效果</p>
              </div>
            </div>
            
            <div className="text-sm text-gray-500">
              预计上线时间：2025年第三季度
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
