import { Policy } from '@/types/policy';

interface PolicyTableProps {
  policies: Policy[];
  pagination: {
    total: number;
    pages: number;
    current_page: number;
    per_page: number;
  };
  onEdit: (policy: Policy) => void;
  onDelete: (id: number) => void;
  onPageChange: (page: number) => void;
  onPerPageChange: (perPage: number) => void;
  onSelectPolicy?: (id: number) => void;
  onSelectAll?: () => void;
  selectedPolicies?: number[];
  loading: boolean;
}

export default function PolicyTable({
  policies,
  pagination,
  onEdit,
  onDelete,
  onPageChange,
  onPerPageChange,
  onSelectPolicy,
  onSelectAll,
  selectedPolicies = [],
  loading
}: PolicyTableProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const formatPassengerType = (passengerType?: string) => {
    if (!passengerType) return '-';
    const typeMap: { [key: string]: string } = {
      'ADT': '成人',
      'CHD': '儿童',
      'INF': '婴儿'
    };
    return passengerType.split(',').map(type => typeMap[type.trim()] || type.trim()).join(', ');
  };

  const formatCommissionRate = (rate?: number) => {
    if (!rate) return '-';
    return `${(rate * 100).toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm shadow-lg rounded-xl p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm shadow-lg rounded-xl overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {onSelectPolicy && onSelectAll && (
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                  <input
                    type="checkbox"
                    checked={selectedPolicies.length > 0 && selectedPolicies.length === policies.length}
                    onChange={onSelectAll}
                    className="h-4 w-4 text-blue-600 rounded cursor-pointer"
                  />
                </th>
              )}
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                旅客类型
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                佣金费率
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                航空公司
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                舱位等级
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                旅行日期
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                销售日期
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {policies.map((policy) => (
              <tr key={policy.id} className="hover:bg-gray-50">
                {onSelectPolicy && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedPolicies.includes(policy.id)}
                      onChange={() => onSelectPolicy(policy.id)}
                      className="h-4 w-4 text-blue-600 rounded cursor-pointer"
                    />
                  </td>
                )}
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatPassengerType(policy.passenger_type)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatCommissionRate(policy.commission_rate)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {policy.airline || '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {policy.cabin_classes || '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{formatDate(policy.travel_start_date)}</div>
                  <div className="text-gray-500">至 {formatDate(policy.travel_end_date)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{formatDate(policy.sale_start_date)}</div>
                  <div className="text-gray-500">至 {formatDate(policy.sale_end_date)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onEdit(policy)}
                      className="text-indigo-600 hover:text-indigo-900 cursor-pointer"
                    >
                      编辑
                    </button>
                    <button
                      onClick={() => onDelete(policy.id)}
                      className="text-red-600 hover:text-red-900 cursor-pointer"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => onPageChange(pagination.current_page - 1)}
            disabled={pagination.current_page === 1}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <button
            onClick={() => onPageChange(pagination.current_page + 1)}
            disabled={pagination.current_page === pagination.pages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              显示第 <span className="font-medium">{(pagination.current_page - 1) * pagination.per_page + 1}</span> 到 <span className="font-medium">{Math.min(pagination.current_page * pagination.per_page, pagination.total)}</span> 条结果，共 <span className="font-medium">{pagination.total}</span> 条
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => onPageChange(pagination.current_page - 1)}
                disabled={pagination.current_page === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
              >
                上一页
              </button>
              {[...Array(pagination.pages)].map((_, i) => {
                const page = i + 1;
                // 只显示当前页和前后各2页，以及第一页和最后一页
                if (page === 1 || page === pagination.pages || (page >= pagination.current_page - 2 && page <= pagination.current_page + 2)) {
                  return (
                    <button
                      key={page}
                      onClick={() => onPageChange(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === pagination.current_page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  );
                } else if (page === pagination.current_page - 3 || page === pagination.current_page + 3) {
                  // 省略号
                  return (
                    <span key={page} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500">
                      ...
                    </span>
                  );
                }
                return null;
              })}
              <button
                onClick={() => onPageChange(pagination.current_page + 1)}
                disabled={pagination.current_page === pagination.pages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </nav>
          </div>
        </div>
      </div>

      {policies.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500">未找到政策</div>
        </div>
      )}
    </div>
  );
}