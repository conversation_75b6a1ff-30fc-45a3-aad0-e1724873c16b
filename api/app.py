import os
import logging
from flask import Flask
from flask_cors import CORS

from config import config
from extensions import db, migrate
from controllers.main import main_bp
from models.policy import Policy  # Import the model

def setup_logging(app):
    """配置应用日志"""
    # 获取日志级别
    log_level = app.config.get('LOG_LEVEL', 'INFO')
    log_file = app.config.get('LOG_FILE', 'logs/app.log')

    # 确保日志目录存在
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 配置文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        file_handler.setLevel(getattr(logging, log_level.upper()))
        app.logger.addHandler(file_handler)

    # 配置控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    app.logger.addHandler(console_handler)

    # 设置应用日志级别
    app.logger.setLevel(getattr(logging, log_level.upper()))

    # 配置根日志器，确保其他模块的日志也能正确输出
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file) if log_file else logging.NullHandler(),
            logging.StreamHandler()
        ]
    )

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 配置日志
    setup_logging(app)

    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    
    # 配置 CORS
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # 注册蓝图
    app.register_blueprint(main_bp)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    return app

if __name__ == '__main__':
    app = create_app()
    host = os.getenv('API_HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', 5000))
    app.run(host=host, port=port, debug=True)
