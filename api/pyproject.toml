[project]
name = "zcwd-api"
version = "0.1.0"
description = "脚手架 API"
requires-python = ">=3.10,<3.13"

dependencies = [
    "flask~=3.0.0",
    "flask-cors~=4.0.0",
    "flask-login~=0.6.3",
    "flask-migrate~=4.0.7",
    "flask-sqlalchemy~=3.1.1",
    "sqlalchemy~=2.0.23",
    "python-dotenv~=1.0.0",
    "pydantic~=2.5.0",
    "requests~=2.31.0",
    "psycopg2-binary~=2.9.9",
    "pandas~=2.1.0",
    "openpyxl~=3.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest~=7.4.0",
    "pytest-mock~=3.12.0",
    "black~=23.12.0",
    "flake8~=6.1.0",
]

[tool.setuptools]
packages = []

[tool.uv]
package = false

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"
